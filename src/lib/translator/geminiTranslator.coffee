{ GoogleGenerativeAI } = require ("@google/generative-ai")
debugHelper = require '../debug'
debug = debugHelper.getDebugger()
AITranslator = require './AITranslator'


class GeminiTranslator extends AITranslator
  constructor: (apiKey,model,prompt,maxUsage) ->
    super(apiKey,null,model,prompt,maxUsage)
    @genAI = new GoogleGenerativeAI(@apiKey)
    @modelGemini = @genAI.getGenerativeModel({model:@model})

  translate: (message, fromLang = 'English', toLang = 'Chinese') ->
    @_executeTranslation("#{@prompt} #{fromLang} to #{toLang}: #{message}")

  ###
  使用自定义prompt进行翻译
  支持字符串prompt和模版对象两种格式

  @param {String} content - 要翻译的内容
  @param {String|Object} customPrompt - 自定义提示词或模版对象
  @param {String} fromLang - 源语言，默认'English'
  @param {String} toLang - 目标语言，默认'Chinese'
  @param {Object} variables - 模版变量值映射（当customPrompt为模版对象时使用）
  @return {String} 翻译结果
  ###
  translateWithCustomPrompt: (content, customPrompt, fromLang = 'English',
                             toLang = 'Chinese', variables = {}) ->
    # 检查是否为模版对象
    if @_isTemplateObject(customPrompt)
      # 处理模版对象
      templateResult = @_processTemplate(customPrompt, variables, content, fromLang, toLang)

      if templateResult.error
        throw new Error("Template processing error: #{templateResult.error}")

      # Gemini使用简单的文本prompt，将系统消息和用户消息合并
      finalPrompt = templateResult.main
      if templateResult.sys
        finalPrompt = "#{templateResult.sys}\n\n#{templateResult.main}"

      @_executeTranslation(finalPrompt)

    else
      # 传统字符串prompt处理（向后兼容）
      @_executeTranslation("#{customPrompt} #{fromLang} to #{toLang}: #{content}")

  ###
  执行翻译请求的通用方法
  ###
  _executeTranslation: (promptMessage) ->
    try
      @use()
      result = await @modelGemini.generateContent(promptMessage)
      response = await result.response
      debug.debug response

      translatedContent = response.text()

      if translatedContent.startsWith('Error')  # Replace this condition as needed
        throw new Error(translatedContent)

      translatedContentDeleteN = translatedContent.replace(/\n+$/, '')
      return translatedContentDeleteN

    catch error
      debug.error 'Gemini Translation API Error:', error.message
      throw error
    finally
      @release()

module.exports = GeminiTranslator