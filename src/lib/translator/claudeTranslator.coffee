debugHelper = require '../debug'
debug = debugHelper.getDebugger()
AITranslator = require './AITranslator'


class ClaudeTranslator extends AITranslator
  constructor: (apiKey,endpoint,model,prompt,maxUsage) ->
    super(apiKey,endpoint,model,prompt,maxUsage)
    
  translate: (message, fromLang = 'English', toLang = 'Chinese') ->
    data = {
      model: @model
      max_tokens: 1024
      messages: [
        { role: 'user', content: "#{@prompt} #{fromLang} to #{toLang}: #{message}" }
      ]
    }
    @_executeTranslation(data)

  ###
  使用自定义prompt进行翻译
  支持字符串prompt和模版对象两种格式

  @param {String} content - 要翻译的内容
  @param {String|Object} customPrompt - 自定义提示词或模版对象
  @param {String} fromLang - 源语言，默认'English'
  @param {String} toLang - 目标语言，默认'Chinese'
  @param {Object} variables - 模版变量值映射（当customPrompt为模版对象时使用）
  @return {String} 翻译结果
  ###
  translateWithCustomPrompt: (content, customPrompt, fromLang = 'English',
                             toLang = 'Chinese', variables = {}) ->
    # 检查是否为模版对象
    if @_isTemplateObject(customPrompt)
      # 处理模版对象
      templateResult = @_processTemplate(customPrompt, variables, content, fromLang, toLang)

      if templateResult.error
        throw new Error("Template processing error: #{templateResult.error}")

      # 构建消息数组
      messages = []

      # Claude支持系统消息，但需要特殊处理
      if templateResult.sys
        messages.push { role: 'system', content: templateResult.sys }

      # 添加用户消息
      messages.push { role: 'user', content: templateResult.main }

      data = {
        model: @model
        max_tokens: 1024
        messages: messages
      }

      # 应用模版中的模型参数（如果存在）
      if templateResult.params
        # 合并参数，但保持Claude特有的参数
        mergedParams = Object.assign {max_tokens: 1024}, templateResult.params
        Object.assign data, mergedParams

    else
      # 传统字符串prompt处理（向后兼容）
      data = {
        model: @model
        max_tokens: 1024
        messages: [
          { role: 'user', content: "#{customPrompt} #{fromLang} to #{toLang}: #{content}" }
        ]
      }

    @_executeTranslation(data)

  ###
  执行翻译请求的通用方法
  ###
  _executeTranslation: (data) ->
    try
      @use()
      response = await fetch(@endpoint,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
          'x-api-key': "#{@apiKey}"
          'anthropic-version': '2023-06-01'
        },
        body: JSON.stringify(data)
      )
      ret = await response.json()
      debug.debug ret
      if response.ok
        translatedContent = ret.content[0].text
        translatedContentDeleteN = translatedContent.replace(/\n+$/, '')
        return translatedContentDeleteN
      else
        throw new Error(ret.message)
    catch error
      debug.error 'Claude Translation API Error:', error.message
      throw error
    finally
      @release()

module.exports = ClaudeTranslator