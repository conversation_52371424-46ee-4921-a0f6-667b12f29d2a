debugHelper = require '../debug'
debug = debugHelper.getDebugger()
AITranslator = require './AITranslator'


class RMTranslator extends AITranslator
  constructor: (endpoint,model,prompt,maxUsage) ->
    super(null,endpoint,model,prompt,maxUsage)
    
  translate: (message, fromLang = 'English', toLang = 'Chinese') ->
    placeNamesPrompt = 'Use the following translations for specific place names: ' +
      'City of Toronto → 多伦多市, Old Toronto → 旧多伦多市, East York → 东约克, ' +
      'Etobicoke → 怡陶碧谷, Scarborough → 士嘉堡, North York → 北约克, York → 约克, ' +
      'Mississauga → 密西沙加, Brampton → 布兰普顿, <PERSON>ham → 万锦, Vaughan → 旺市, ' +
      'Richmond Hill → 列治文山, Oakville → 奥克维尔, Burlington → 伯灵顿, ' +
      'Pickering → 皮克灵, Ajax → 阿贾克斯, Whitby → 惠特比, Oshawa → 奥沙瓦, ' +
      'Clarington → 克拉灵顿, Uxbridge → 厄克斯桥, Scugog → 斯库高, Brock → 布鲁克, ' +
      'Newmarket → 新市, Aurora → 奥罗拉, <PERSON> → 金, East Gwillimbury → 东贵林, ' +
      'Georgina → 乔治娜, Whitchurch-Stouffville → 圣维尔, Milton → 米尔顿, ' +
      'Halton Hills → 荷顿山, Caledon → 卡利登, ' +
      'U of T Scarborough Campus → 多伦多大学士嘉堡校区. Do not translate other place names.'

    data = {
      model: @model
      prompt: "#{@prompt} #{fromLang} to #{toLang}, #{placeNamesPrompt} " +
        "Only return the translated text: #{message}"
      stream: false
    }
    @_executeTranslation(data)

  ###
  使用自定义prompt进行翻译
  支持字符串prompt和模版对象两种格式

  @param {String} content - 要翻译的内容
  @param {String|Object} customPrompt - 自定义提示词或模版对象
  @param {String} fromLang - 源语言，默认'English'
  @param {String} toLang - 目标语言，默认'Chinese'
  @param {Object} variables - 模版变量值映射（当customPrompt为模版对象时使用）
  @return {String} 翻译结果
  ###
  translateWithCustomPrompt: (content, customPrompt, fromLang = 'English',
                             toLang = 'Chinese', variables = {}) ->
    # 检查是否为模版对象
    if @_isTemplateObject(customPrompt)
      # 处理模版对象
      templateResult = @_processTemplate(customPrompt, variables, content, fromLang, toLang)

      if templateResult.error
        throw new Error("Template processing error: #{templateResult.error}")

      # RM翻译器使用简单的prompt格式，将系统消息和用户消息合并
      finalPrompt = templateResult.main
      if templateResult.sys
        finalPrompt = "#{templateResult.sys}\n\n#{templateResult.main}"

      data = {
        model: @model
        prompt: finalPrompt
        stream: false
      }

      # 应用模版中的模型参数（如果存在）
      if templateResult.params
        Object.assign data, templateResult.params

    else
      # 传统字符串prompt处理（向后兼容）
      data = {
        model: @model
        prompt: "#{customPrompt} #{fromLang} to #{toLang}: #{content}"
        stream: false
      }

    @_executeTranslation(data)

  ###
  执行翻译请求的通用方法
  ###
  _executeTranslation: (data) ->
    try
      @use()
      response = await fetch(@endpoint,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      )
      debug.debug response
      debug.debug 'done###response', response.status
      if not response.ok
        throw new Error("HTTP error! status: #{response.status}")

      ret = await response.json()
      debug.debug ret

      if ret.response?
        translatedContent = ret.response
        translatedContentDeleteN = translatedContent.replace(/\n/g, '')
        return translatedContentDeleteN
      else
        throw new Error('Empty response from translation API')
    catch error
      debug.error 'RM Translation API Error:', {
        error: {
          name: error.name
          message: error.message
          stack: error.stack
          details: error
        }
        response: ret || 'No response parsed'
      }
      throw error
    finally
      @release()

module.exports = RMTranslator