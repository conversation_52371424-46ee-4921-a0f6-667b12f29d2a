debugHelper = require '../debug'
debug = debugHelper.getDebugger()
AITranslator = require './AITranslator'
ERR_THRESHOLD=0.2

class DeepSeekTranslator extends AITranslator
  constructor: (apiKey,endpoint,model,prompt,maxUsage) ->
    super(apiKey,endpoint,model,prompt,maxUsage)
    

  translate: (message, fromLang = 'English', toLang = 'Chinese') ->
    data = {
      model: @model
      messages: [
        { role: 'user', content: "#{@prompt} #{fromLang} to #{toLang}: #{message}" }
      ]
      stream: false
    }
    @_executeTranslation(data)

  ###
  使用自定义prompt进行翻译
  支持字符串prompt和模版对象两种格式

  @param {String} content - 要翻译的内容
  @param {String|Object} customPrompt - 自定义提示词或模版对象
  @param {String} fromLang - 源语言，默认'English'
  @param {String} toLang - 目标语言，默认'Chinese'
  @param {Object} variables - 模版变量值映射（当customPrompt为模版对象时使用）
  @return {String} 翻译结果
  ###
  translateWithCustomPrompt: (content, customPrompt, fromLang = 'English',
                             toLang = 'Chinese', variables = {}) ->
    # 检查是否为模版对象
    if @_isTemplateObject(customPrompt)
      # 处理模版对象
      templateResult = @_processTemplate(customPrompt, variables, content, fromLang, toLang)

      if templateResult.error
        throw new Error("Template processing error: #{templateResult.error}")

      # 构建消息数组
      messages = []

      # 添加系统消息（如果存在）
      if templateResult.sys
        messages.push { role: 'system', content: templateResult.sys }

      # 添加用户消息
      messages.push { role: 'user', content: templateResult.main }

      data = {
        model: @model
        messages: messages
        stream: false
      }

      # 应用模版中的模型参数（如果存在）
      if templateResult.params
        Object.assign data, templateResult.params

    else
      # 传统字符串prompt处理（向后兼容）
      data = {
        model: @model
        messages: [
          { role: 'user', content: "#{customPrompt} #{fromLang} to #{toLang}: #{content}" }
        ]
        stream: false
      }

    @_executeTranslation(data)

  ###
  执行翻译请求的通用方法
  ###
  _executeTranslation: (data) ->
    try
      @use()
      response = await fetch(@endpoint,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
          'Authorization': "Bearer #{@apiKey}"
        },
        body: JSON.stringify(data)
        signal: AbortSignal.timeout(30000)  # 30 seconds timeout
      )
      ret = await response.json()
      debug.debug ret
      if response.ok
        translatedContent = ret.choices[0].message.content
        # check if the return is too short
        threshold = Math.ceil(message.length * ERR_THRESHOLD)
        if translatedContent.length < threshold
          debug.error 'Translation result is too short', translatedContent
          # 不用throw，直接返回空字符串
          return ''
        translatedContentDeleteN = translatedContent.replace(/\n+$/, '')
        return translatedContentDeleteN
      else
        err = new Error(ret.error.message)
        err.name = ret.error.type
        throw err
    catch error
      if error.name is 'TimeoutError'
        debug.error 'Timeout: It took more than 30 seconds to get the result!'
      if error.name is 'authentication_error'
        debug.error 'DeepSeek Translation API Authentication Error, check key'
      else
        debug.error 'DeepSeek Translation API Error:', error.message
      throw error
    finally
      @release()

module.exports = DeepSeekTranslator