Translator = require './translator'

class AITranslator extends Translator
  constructor: (apiKey, endpoint, model, prompt, maxUsage) ->
    super(apiKey, endpoint, maxUsage)
    @model = model
    @prompt = prompt

  ###
  使用自定义prompt进行翻译（子类需要实现）
  支持字符串prompt和模版对象两种格式，保持向后兼容性

  @param {String} content - 要翻译的内容
  @param {String|Object} customPrompt - 自定义提示词或模版对象
    - String: 传统的字符串prompt（向后兼容）
    - Object: 模版对象，包含以下字段：
      - tpl.main: 主要提示模板内容
      - tpl.sys: 系统提示词（可选）
      - m_cfg.m_nm: AI模型名称（可选）
      - m_cfg.params: 模型参数（可选）
      - vars: 变量定义数组（可选）
  @param {String} fromLang - 源语言
  @param {String} toLang - 目标语言
  @param {Object} variables - 模版变量值映射（当customPrompt为模版对象时使用）
  @return {String} 翻译结果
  ###
  translateWithCustomPrompt: (content, customPrompt, fromLang, toLang, variables = {}) ->
    throw new Error('translateWithCustomPrompt method must be implemented by subclass')

  ###
  处理模版对象，解析并替换变量
  @param {Object} template - 模版对象
  @param {Object} variables - 变量值映射
  @param {String} content - 要翻译的内容
  @param {String} fromLang - 源语言
  @param {String} toLang - 目标语言
  @return {Object} 处理结果 {main: string, sys?: string, error?: string}
  ###
  _processTemplate: (template, variables, content, fromLang, toLang) ->
    # 参数验证
    unless template?.tpl?.main
      return {error: 'Invalid template structure: missing tpl.main'}

    # 准备默认变量
    defaultVariables = {
      text: content
      content: content
      source_language: fromLang
      target_language: toLang
      from_lang: fromLang
      to_lang: toLang
    }

    # 合并用户提供的变量和默认变量
    allVariables = Object.assign {}, defaultVariables, variables

    # 检查必填变量
    if template.vars
      for variable in template.vars
        if variable.req and not allVariables[variable.nm]?
          return {error: "Missing required variable: #{variable.nm}"}

    # 替换主模板内容中的变量
    mainContent = template.tpl.main
    for varName, varValue of allVariables
      if varValue?
        # 使用正则表达式替换 {变量名} 格式的占位符
        regex = new RegExp("\\{#{varName}\\}", 'g')
        mainContent = mainContent.replace(regex, String(varValue))

    # 替换系统提示词中的变量（如果存在）
    sysContent = template.tpl.sys
    if sysContent
      for varName, varValue of allVariables
        if varValue?
          regex = new RegExp("\\{#{varName}\\}", 'g')
          sysContent = sysContent.replace(regex, String(varValue))

    return {
      main: mainContent
      sys: sysContent
      model: template.m_cfg?.m_nm
      params: template.m_cfg?.params or {}
    }

  ###
  检查参数是否为模版对象
  @param {String|Object} prompt - 提示词参数
  @return {Boolean} 是否为模版对象
  ###
  _isTemplateObject: (prompt) ->
    return prompt and
           typeof prompt is 'object' and
           prompt.tpl? and
           typeof prompt.tpl.main is 'string'

module.exports = AITranslator