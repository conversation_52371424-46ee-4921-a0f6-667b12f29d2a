###
TranslatorManager - 多服务翻译管理器

支持传统翻译和基于外部Prompt的智能翻译功能，包括：
- 多种翻译服务的统一管理和容错机制
- 基于外部Prompt的翻译功能（Prompt由调用端传入）
- LLM评论过滤功能（使用外部过滤Prompt）
- i18n过滤规则集成
- 资源管理和等待队列机制

使用示例：

```coffeescript
# 创建翻译管理器
config = {
  openAI: { key: 'your-key', endpoint: 'https://api.openai.com/v1/chat/completions' }
  gemini: { key: 'your-key' }
}
translatorManager = require('./translatorManager').createTranslatorManager(config)

# 传统翻译
[result, service] = await translatorManager.translate('Hello world')

# 基于外部Prompt的翻译 - UI文本（应用i18n过滤）
uiPrompt = 'Please translate the following UI text to Chinese: '
result = await translatorManager.translateWithPrompt(
  'Save changes',
  uiPrompt,
  'zh-cn'
)

# 基于外部Prompt的翻译 - 论坛内容（跳过i18n过滤）
forumPrompt = 'Please translate the following forum post to Chinese: '
result = await translatorManager.translateWithPrompt(
  'This property has excellent location and amenities.',
  forumPrompt,
  'zh-cn',
  { skipI18nFilter: true }
)

# 评论过滤（使用外部过滤Prompt）
filterPrompt = 'Please check if this content is appropriate. Reply PASS or REJECT: '
filterResult = await translatorManager.filterComments(
  'This is a test comment',
  filterPrompt,
  { language: 'en' }
)
```

<AUTHOR> Team
@version 2.0.0
@since 2025-06-30
###

debugHelper = require '../debug'
debug = debugHelper.getDebugger()
AzureTranslator = require './azureTranslator'
DeepLTranslator = require './deepLTranslator'
DeepSeekTranslator = require './deepseekTranslator'
OpenAITranslator = require './openAITranslator'
GeminiTranslator = require './geminiTranslator'
ClaudeTranslator = require './claudeTranslator'
GrokTranslator = require './grokTranslator'
RMTranslator = require './rmTranslator'
{i18nRegexArray} = require '../i18n'

# 默认翻译提示词（保持向后兼容）
DEFAULT_PROMPT = 'You are a real estate expert. The following content is about ' +
  'Canada real estate listing. Please translate all of the following content, ' +
  'but do not translate place names. Only return the translated text, ' +
  'do not include any other text. Translate the following text from English to Chinese.'

# 模型配置常量
MODEL_DEEPSEEK = 'deepseek-chat'
MODEL_OPENAI = 'gpt-4o-mini'
MODEL_GEMINI = 'gemini-2.0-flash-lite' # 'gemini-1.5-flash'
MODEL_CLAUDE = 'claude-3-haiku-20240307'
MODEL_GROK = 'grok-2-latest'
MODEL_RM = 'gemma3:12b'
# grok can not be used in app now because of the request limit
TRANSLATORLIST = ['gemini', 'openAI', 'claude', 'azure']

LANGUAGE_OBJ = [
  {k: 'en', v:'English', AIValue: 'English', azureValue: 'en', deepLValue:'EN'},
  {k: 'zh-cn', v:'简体中文', AIValue: 'Chinese', azureValue: 'zh-Hans', deepLValue:'ZH'},
  {k: 'zh', v:'繁体中文', AIValue: 'Traditional Chinese', azureValue: 'zh-Hant', deepLValue:'ZH-HANT'},
  {k: 'kr', v:'한국어', AIValue: 'Korean', azureValue: 'ko', deepLValue:'KO'},
]

findLanguageValue = (k, service) ->
  result = LANGUAGE_OBJ.find (language) -> language.k is k
  if result?
    switch service
      when 'azure' then result.azureValue
      when 'deepl' then result.deepLValue
      when 'deepseek', 'openAI', 'gemini', 'claude', 'grok', 'rm' then result.AIValue
      else throw new Error("Service '#{service}' not supported")
  else
    throw new Error("Key '#{k}' not found in LANGUAGE_OBJ")

class TranslatorManager
  ###
  TranslatorManager - 多服务翻译管理器
  支持传统翻译和基于Prompt模板的智能翻译功能

  @param {Object} config - 配置对象，包含各种翻译服务的配置信息
  ###
  constructor: (config) ->
    debug.debug 'TranslatorManager config:', config
    @translators = {}
    @waitingQueue = []
    @i18nRegexArray = i18nRegexArray

    # Initialize translators with their max usage limits from config
    if config?.azure?.subscriptionKey
      @translators['azure'] = new AzureTranslator(
        config.azure.subscriptionKey,
        config.azure.endpoint,
        config.azure.region,
        config.azure.maxUsage
      )

    if config?.deepL?.key
      @translators['deepl'] = new DeepLTranslator(
        config.deepL.key,
        config.deepL.endpoint,
        config.deepL.maxUsage
      )

    if config?.deepseek?.key
      @translators['deepseek'] = new DeepSeekTranslator(
        config.deepseek.key,
        config.deepseek.endpoint,
        MODEL_DEEPSEEK,
        DEFAULT_PROMPT,
        config.deepseek.maxUsage
      )

    if config?.openAI?.key
      @translators['openAI'] = new OpenAITranslator(
        config.openAI.key,
        config.openAI.endpoint,
        MODEL_OPENAI,
        DEFAULT_PROMPT,
        config.openAI.orgID,
        config.openAI.projectID,
        config.openAI.maxUsage
      )

    if config?.gemini?.key
      @translators['gemini'] = new GeminiTranslator(
        config.gemini.key,
        MODEL_GEMINI,
        DEFAULT_PROMPT,
        config.gemini.maxUsage
      )

    if config?.claude?.key
      @translators['claude'] = new ClaudeTranslator(
        config.claude.key,
        config.claude.endpoint,
        MODEL_CLAUDE,
        DEFAULT_PROMPT,
        config.claude.maxUsage
      )

    if config?.grok?.key
      debug.debug 'Initializing Grok translator with key:', config.grok.key
      @translators['grok'] = new GrokTranslator(
        config.grok.key,
        config.grok.endpoint,
        MODEL_GROK,
        DEFAULT_PROMPT,
        config.grok.maxUsage
      )

    if config?.rm?.endpoint
      @translators['rm'] = new RMTranslator(
        config.rm.endpoint,
        MODEL_RM,
        DEFAULT_PROMPT,
        config.rm.maxUsage
      )

  ###
  获取可用的翻译服务
  @param {Array} translatorList - 翻译服务列表
  @return {String|null} 可用的服务名称或null
  ###
  getAvailableTranslator: (translatorList) ->
    debug.debug '###getAvailableTranslator', translatorList
    # Check translators in the order of translatorList
    for service in translatorList
      translator = @translators[service]
      if translator?
        debug.debug "###service #{service}: usage=#{translator.usageCount}, " +
          "max=#{translator.maxUsage}, available=#{translator.isAvailable()}"
        if translator.isAvailable()
          return service
    return null

  processWaitingQueue: ->
    debug.debug '###processWaitingQueue',@waitingQueue.length
    if @waitingQueue.length > 0
      # Get the first waiting resolve function and execute it
      resolve = @waitingQueue.shift()
      resolve()

  ###
  检查内容是否应该被过滤（不进行翻译）
  @param {String} content - 要检查的内容
  @return {Boolean} true表示应该过滤，false表示可以翻译
  ###
  _shouldFilterContent: (content) ->
    return true unless content and ('string' is typeof content)
    return true if content.trim().length is 0

    # 使用i18n过滤规则
    if @i18nRegexArray
      for regexObj in @i18nRegexArray
        if regexObj.regex.test(content)
          debug.debug "Content filtered by i18n rule: #{regexObj.message}", content
          return true

    return false



  ###
  检查参数是否为模版对象
  @param {String|Object} prompt - 提示词参数
  @return {Boolean} 是否为模版对象
  ###
  _isTemplateObject: (prompt) ->
    return prompt and
           typeof prompt is 'object' and
           prompt.tpl? and
           typeof prompt.tpl.main is 'string'

  ###
  使用自定义Prompt进行翻译
  支持字符串prompt和模版对象两种格式

  @param {String} content - 翻译内容
  @param {String|Object} customPrompt - 自定义提示词或模版对象
  @param {String} fromLangKey - 源语言键
  @param {String} toLangKey - 目标语言键
  @param {Array} translatorList - 翻译服务列表
  @param {Object} variables - 模版变量值映射（当customPrompt为模版对象时使用）
  @return {Array} [翻译结果, 使用的服务名称]
  ###
  _translateWithCustomPrompt: (content, customPrompt, fromLangKey, toLangKey,
                              translatorList, variables = {}) ->
    attemptTranslation = (index) =>
      if index >= translatorList.length
        throw new Error('Translation failed with all services')

      service = translatorList[index]
      translator = @translators[service]

      if translator?
        try
          fromLang = findLanguageValue(fromLangKey, service)
          toLang = findLanguageValue(toLangKey, service)

          # Check if translator is available
          if not translator.isAvailable()
            debug.debug "Translator #{service} is at max usage, trying next service"
            return attemptTranslation(index + 1)

          debug.debug "Using translator #{service} with custom prompt"

          # 检查翻译器类型并相应处理
          if translator.translateWithCustomPrompt?
            # LLM翻译器支持自定义prompt
            result = await translator.translateWithCustomPrompt(content, customPrompt, fromLang, toLang, variables)
          else
            # 传统翻译器（如Azure, DeepL）不支持prompt，直接翻译内容
            result = await translator.translate(content, fromLang, toLang)

          # Process waiting queue after translation
          @processWaitingQueue()

          if result and result isnt ''
            return [result, service]

          debug.warn "Empty translation result from #{service}, trying next service"
          return attemptTranslation(index + 1)
        catch error
          # Process waiting queue on error
          @processWaitingQueue()

          if error.name is 'authentication_error'
            debug.error "#{service} Translation API Authentication Error"
            throw error
          else
            debug.error "Error using #{service} translator with custom prompt:", {
              errorName: error.name
              errorMessage: error.message
              service: service
            }
            return attemptTranslation(index + 1)
      else
        debug.error "Translator service not found: #{service}"
        return attemptTranslation(index + 1)

    # Try to get an available translator
    service = @getAvailableTranslator(translatorList)
    if not service?
      # If no translator is available, add to waiting queue
      debug.debug 'No translator available, adding to waiting queue'
      await new Promise((resolve) =>
        @waitingQueue.push resolve
      )
      # After being woken up, try again
      service = @getAvailableTranslator(translatorList)
      if not service?
        throw new Error('No translator available after waiting')

    # Start translation with the available translator
    return await attemptTranslation(translatorList.indexOf(service))

  ###
  传统翻译方法（保持向后兼容）
  @param {String} message - 要翻译的消息
  @param {Array} translatorList - 翻译服务列表
  @param {String} fromLangKey - 源语言键
  @param {String} toLangKey - 目标语言键
  @return {Array} [翻译结果, 使用的服务名称]
  ###
  translate: (message, translatorList=TRANSLATORLIST, fromLangKey='en', toLangKey='zh-cn') ->
    attemptTranslation = (index) =>
      if index >= translatorList.length
        throw new Error('Translation failed with all services')

      service = translatorList[index]
      translator = @translators[service]

      if translator?
        try
          fromLang = findLanguageValue(fromLangKey, service)
          toLang = findLanguageValue(toLangKey, service)

          # Check if translator is available
          if not translator.isAvailable()
            debug.debug "Translator #{service} is at max usage, trying next service"
            return attemptTranslation(index + 1)

          debug.debug "Using translator #{service}"

          result = await translator.translate(message, fromLang, toLang)

          # Process waiting queue after translation
          @processWaitingQueue()

          if result and result isnt ''
            return [result, service]

          debug.warn "Empty translation result from #{service}, trying next service"
          return attemptTranslation(index + 1)
        catch error
          # Process waiting queue on error
          @processWaitingQueue()

          if error.name is 'authentication_error'
            debug.error "#{service} Translation API Authentication Error"
            throw error
          else
            debug.error "Error using #{service} translator:", {
              errorName: error.name
              errorMessage: error.message
              errorDetails: error
              service: service
            }
            return attemptTranslation(index + 1)
      else
        debug.error "Translator service not found: #{service}"
        return attemptTranslation(index + 1)

    # Try to get an available translator
    service = @getAvailableTranslator(translatorList)
    if not service?
      # If no translator is available, add to waiting queue
      debug.debug 'No translator available, adding to waiting queue'
      await new Promise((resolve) =>
        @waitingQueue.push resolve
      )
      # After being woken up, try again
      service = @getAvailableTranslator(translatorList)
      if not service?
        throw new Error('No translator available after waiting')

    # Start translation with the available translator
    return await attemptTranslation(translatorList.indexOf(service))

  ###
  基于外部Prompt的翻译功能
  接收外部传入的prompt进行翻译，不依赖内部模板选择
  支持字符串prompt和模版对象两种格式

  @param {String} content - 要翻译的内容
  @param {String|Object} prompt - 外部传入的翻译提示词或模版对象
  @param {String} targetLang - 目标语言键 (默认: 'zh-cn')
  @param {Object} options - 选项参数
  @param {String} options.fromLang - 源语言键 (默认: 'en')
  @param {Array} options.translatorList - 翻译服务列表 (默认: TRANSLATORLIST)
  @param {Boolean} options.skipI18nFilter - 是否跳过i18n过滤检查 (默认: false)
    - false: 应用i18n过滤规则，适用于UI文本、系统消息等固定内容
    - true: 跳过i18n过滤，适用于论坛内容、用户评论、房产描述等动态内容
  @param {Object} options.variables - 模版变量值映射（当prompt为模版对象时使用）
  @return {Object} 翻译结果对象 {success: boolean, result?: string, service?: string, error?: string}
  ###
  translateWithPrompt: (content, prompt, targetLang = 'zh-cn', options = {}) ->
    try
      # 参数验证
      unless content and ('string' is typeof content)
        return {success: false, error: 'Invalid content parameter'}

      # 验证prompt参数（支持字符串和模版对象）
      unless prompt and (typeof prompt is 'string' or @_isTemplateObject(prompt))
        return {success: false, error: 'Invalid prompt parameter: must be string or template object'}

      # 根据选项决定是否应用i18n过滤
      skipI18nFilter = options.skipI18nFilter or false

      unless skipI18nFilter
        # 检查内容是否应该被过滤（仅当未跳过过滤时）
        if @_shouldFilterContent(content)
          debug.debug 'Content filtered by i18n rules, skipping translation:', content
          return {success: false, error: 'Content filtered by i18n rules', filtered: true}

      # 设置默认选项
      fromLang = options.fromLang or 'en'
      translatorList = options.translatorList or TRANSLATORLIST
      variables = options.variables or {}

      debug.debug "Using external prompt for translation, skipI18nFilter: #{skipI18nFilter}"

      # 使用外部传入的Prompt进行翻译
      [result, service] = await @_translateWithCustomPrompt(
        content,
        prompt,
        fromLang,
        targetLang,
        translatorList,
        variables
      )

      return {
        success: true
        result: result
        service: service
        prompt: prompt
        skipI18nFilter: skipI18nFilter
      }

    catch error
      debug.error 'Error in translateWithPrompt:', error
      return {success: false, error: error.message}

  ###
  评论过滤功能
  使用外部传入的过滤prompt对评论内容进行LLM过滤和审核

  @param {String} content - 要过滤的评论内容
  @param {String} filterPrompt - 外部传入的过滤提示词
  @param {Object} options - 选项参数
  @param {Array} options.translatorList - 翻译服务列表 (默认: TRANSLATORLIST)
  @param {String} options.language - 内容语言 (默认: 'en')
  @return {Object} 过滤结果对象 {success: boolean, passed: boolean, reason?: string, service?: string}
  ###
  filterComments: (content, filterPrompt, options = {}) ->
    try
      # 参数验证
      unless content and ('string' is typeof content)
        return {success: false, error: 'Invalid content parameter'}

      unless filterPrompt and ('string' is typeof filterPrompt)
        return {success: false, error: 'Invalid filterPrompt parameter'}

      # 基本内容检查
      if content.trim().length is 0
        return {success: true, passed: false, reason: 'Empty content'}

      # 设置默认选项
      translatorList = options.translatorList or TRANSLATORLIST
      language = options.language or 'en'

      debug.debug "Using external filter prompt for comment filtering"

      # 使用LLM进行内容过滤
      [result, service] = await @_translateWithCustomPrompt(
        content,
        filterPrompt,
        language,
        language, # 过滤不需要翻译，保持同一语言
        translatorList
      )

      # 解析过滤结果
      # 假设LLM返回格式为 "PASS" 或 "REJECT: reason"
      passed = false
      reason = 'Unknown filter result'

      if result
        resultLower = result.toLowerCase().trim()
        if resultLower.includes('pass') or resultLower.includes('approved')
          passed = true
          reason = 'Content approved'
        else if resultLower.includes('reject') or resultLower.includes('denied')
          passed = false
          # 尝试提取拒绝原因
          reasonMatch = result.match(/reject[ed]?:?\s*(.+)/i)
          reason = reasonMatch?[1]?.trim() or 'Content rejected'
        else
          # 如果结果不明确，默认拒绝
          passed = false
          reason = 'Unclear filter result'

      return {
        success: true
        passed: passed
        reason: reason
        service: service
        filterPrompt: filterPrompt
      }

    catch error
      debug.error 'Error in filterComments:', error
      # 过滤失败时默认拒绝
      return {success: false, passed: false, error: error.message}

exports.createTranslatorManager = (config)-> new TranslatorManager(config)
exports.findLanguageValue = findLanguageValue
