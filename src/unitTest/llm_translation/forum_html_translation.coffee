###
论坛HTML内容翻译测试
测试增强后的论坛翻译模版和Comments专用模版的HTML处理能力
###

helpers = require('../00_common/helpers')
should = require 'should'
debug = null
llmHelper = require('../../built/libapp/llmTranslationHelper')
translatorManager = require('../../built/lib/translator/translatorManager').createTranslatorManager({
  openAI: { 
    key: 'test-key', 
    endpoint: 'https://api.openai.com/v1/chat/completions' 
  }
})

describe '论坛HTML内容翻译测试', ->
  before (done) ->
    @timeout(30000)
    debug = helpers.DEBUG()
    done()
  
  describe '智能场景选择测试', ->
    it '应该识别评论上下文并选择comment_translation场景', (done) ->
      # 测试评论场景识别
      result1 = llmHelper.selectTranslationScenario('这是一个用户评论', 'comment')
      should.equal result1, 'comment_translation'
      
      result2 = llmHelper.selectTranslationScenario('用户评论内容', '评论')
      should.equal result2, 'comment_translation'
      
      result3 = llmHelper.selectTranslationScenario('Comment content', 'cmnt')
      should.equal result3, 'comment_translation'
      
      done()
    
    it '应该保持原有场景选择逻辑不变', (done) ->
      # 房产场景
      result1 = llmHelper.selectTranslationScenario('bedroom', '房产信息')
      should.equal result1, 'property_translation'
      
      # UI场景
      result2 = llmHelper.selectTranslationScenario('save', '')
      should.equal result2, 'ui_translation'
      
      # 论坛场景
      longText = 'This is a very long forum post that contains multiple sentences and should be classified as forum content for translation purposes because it exceeds the length threshold.'
      longText += longText
      result3 = llmHelper.selectTranslationScenario(longText, '')
      should.equal result3, 'forum_translation'
      
      done()
  
  describe 'HTML内容翻译模拟测试', ->
    it '应该正确处理包含HTML标签的论坛内容', (done) ->
      # 模拟HTML内容
      htmlContent = '<p class="content">这是一段论坛内容</p><div><strong>重要提醒</strong></div>'
      
      # 测试场景选择
      scenario = llmHelper.selectTranslationScenario(htmlContent, 'forum')
      should.equal scenario, 'forum_translation'
      
      # 注意：实际的翻译测试需要真实的API密钥，这里只测试场景选择逻辑
      done()
    
    it '应该正确处理包含HTML标签的评论内容', (done) ->
      # 模拟HTML评论内容
      commentContent = '<span class="highlight">很好的房子</span>，推荐！'
      
      # 测试场景选择
      scenario = llmHelper.selectTranslationScenario(commentContent, 'comment')
      should.equal scenario, 'comment_translation'
      
      done()
  
  describe '模版兼容性测试', ->
    it '应该与现有translateWithPrompt方法兼容', (done) ->
      # 测试参数结构
      testContent = 'Test content'
      testPrompt = 'Translate this content'
      testOptions = { skipI18nFilter: true }
      
      # 验证参数类型
      should.equal typeof testContent, 'string'
      should.equal typeof testPrompt, 'string'
      should.equal typeof testOptions, 'object'
      should.equal testOptions.skipI18nFilter, true
      
      done()
    
    it '应该支持skipI18nFilter选项', (done) ->
      # 测试选项传递
      options = { skipI18nFilter: true, fromLang: 'en' }
      
      should.equal options.skipI18nFilter, true
      should.equal options.fromLang, 'en'
      
      done()

  describe 'HTML处理指令验证', ->
    
    it '应该包含正确的HTML处理指令', (done) ->
      # 这里我们验证模版结构是否正确
      # 实际的模版内容验证需要查询数据库
      
      # 验证HTML处理要求
      htmlRequirements = [
        '只翻译HTML标签内的文本内容',
        '保持HTML标签结构不变',
        '保持所有属性不变'
      ]
      
      should.equal htmlRequirements.length, 3
      done()
    
    it '应该提供正确的HTML处理示例', (done) ->
      # 验证示例格式
      exampleInput = '<p class="content">这是内容</p>'
      expectedPattern = '<p class="content">[translated text]</p>'
      
      # 验证示例结构
      should.equal exampleInput.includes('<p class="content">'), true
      should.equal expectedPattern.includes('<p class="content">'), true
      
      done()
