###
Prompts Model - LLM翻译模板管理系统

数据结构定义 (prompts集合):
@collection prompts
@database chome

@field {String} _id - 业务唯一标识符，格式：场景_用途_模型_版本 (如: "ui_menu_gpt4_v1")
@field {String} nm - 模板显示名称，用于管理界面显示和用户识别
@field {String} desc - 模板详细描述，说明模板的用途和特点 (可选)
@field {Integer} ver - 版本号，数字版本便于排序和比较，支持版本演进
@field {String} status - 模板状态，枚举值: active, draft, deprecated
@field {String} scenario - 主要应用场景，枚举值: ui_translation, forum_translation, comment_filter, general
@field {Array[String]} tags - 灵活标签数组，支持跨场景的灵活分类和搜索 (可选)
@field {Object} m_cfg - AI模型配置信息聚合
@field {String} m_cfg.m_nm - AI模型名称，支持: gpt, claude, deepseek, gemini
@field {Object} m_cfg.params - 模型参数配置，包含temperature、max_tokens、top_p等 (可选)
@field {Object} tpl - 模板内容聚合
@field {String} tpl.main - 主要提示模板内容，核心的提示词模板，支持变量替换
@field {String} tpl.sys - 系统提示词，用于设定AI的角色和行为准则 (可选)
@field {Array[Object]} vars - 模板变量定义数组 (可选)
@field {String} vars[].nm - 变量名称，在模板中使用{变量名}格式引用
@field {String} vars[].tp - 变量数据类型: string, number, array
@field {Boolean} vars[].req - 是否为必填变量
@field {String} vars[].desc - 变量说明，帮助开发者理解变量用途 (可选)
@field {ISODate} ts - 创建时间，模板首次创建的时间戳
@field {ISODate} _mt - 最后更新时间，模板最后一次修改的时间戳

示例数据:
{
  "_id": "ui_menu_gpt4_v1",
  "nm": "UI菜单翻译模板",
  "desc": "专用于网页界面菜单项的翻译",
  "ver": 1,
  "status": "active",
  "scenario": "ui_translation",
  "tags": ["ui", "menu", "short_text"],
  "m_cfg": {
    "m_nm": "gpt",
    "params": {"temperature": 0.3, "max_tokens": 100}
  },
  "tpl": {
    "main": "请将以下英文UI术语翻译为{target_language}: {text}",
    "sys": "你是一个专业的UI界面翻译专家，请提供准确、简洁的翻译。"
  },
  "vars": [
    {"nm": "text", "tp": "string", "req": true, "desc": "需要翻译的UI文本"},
    {"nm": "target_language", "tp": "string", "req": true, "desc": "目标语言"}
  ],
  "ts": ISODate("2025-06-23T09:30:00.000Z"),
  "_mt": ISODate("2025-06-23T10:00:00.000Z")
}
###

debug = DEBUG()
{isAlphaNumeric, isStringWithValue} = INCLUDE 'lib.helpers_string'
{isNumber} = INCLUDE 'lib.helpers_number'
ObjectId = INCLUDE('lib.mongo4').ObjectId

PromptsCol = COLLECTION 'chome', 'prompts'
config = CONFIG(['serverBase'])

# 创建索引以优化查询性能
if config.serverBase?.masterMode
  PromptsCol.createIndex {'status': 1}
  PromptsCol.createIndex {'scenario': 1}
  PromptsCol.createIndex {'tags': 1}
  PromptsCol.createIndex {'ver': -1}
  PromptsCol.createIndex {'_mt': -1}

# 枚举值定义
VALID_STATUSES = ['active', 'draft', 'deprecated']
VALID_SCENARIOS = [
  'ui_translation', 'forum_translation', 'comment_translation', 'comment_filter', 'general'
]
VALID_MODELS = ['gpt', 'claude', 'deepseek', 'gemini']
VALID_VAR_TYPES = ['string', 'number', 'array']

class Prompts
  ###
  私有函数：验证prompt模板数据
  @param {Object} promptData - 模板数据
  @param {Boolean} isUpdate - 是否为更新操作
  @return {Object} 验证结果 {valid: boolean, errors: string[]}
  ###
  @_validatePromptData: (promptData, isUpdate = false) ->
    errors = []

    # 基础字段验证
    unless promptData._id and isStringWithValue(promptData._id)
      errors.push 'Missing or invalid _id field'

    unless promptData.nm and isStringWithValue(promptData.nm)
      errors.push 'Missing or invalid nm field'

    unless isNumber(promptData.ver) and promptData.ver > 0
      errors.push 'Missing or invalid ver field'

    unless promptData.status in VALID_STATUSES
      errors.push "Invalid status. Must be one of: #{VALID_STATUSES.join(', ')}"

    unless promptData.scenario in VALID_SCENARIOS
      errors.push "Invalid scenario. Must be one of: #{VALID_SCENARIOS.join(', ')}"

    unless promptData.m_cfg?.m_nm in VALID_MODELS
      errors.push "Invalid model name. Must be one of: #{VALID_MODELS.join(', ')}"

    unless promptData.tpl?.main and isStringWithValue(promptData.tpl.main)
      errors.push 'Missing or invalid tpl.main field'

    # 变量定义验证
    if promptData.vars
      unless Array.isArray(promptData.vars)
        errors.push 'vars field must be an array'
      else
        for i, variable of promptData.vars
          unless variable.nm and isStringWithValue(variable.nm)
            errors.push "Invalid variable name at index #{i}"
          unless variable.tp in VALID_VAR_TYPES
            validTypes = VALID_VAR_TYPES.join(', ')
            errors.push "Invalid variable type at index #{i}. Must be one of: #{validTypes}"
          unless typeof variable.req is 'boolean'
            errors.push "Variable req field must be boolean at index #{i}"

    return {
      valid: errors.length is 0
      errors: errors
    }

  ###
  保存prompt模板（创建或更新）
  @param {Object} promptData - 模板数据
  @param {Boolean} isUpdate - 是否为更新操作，默认false
  @return {Object} 保存结果或错误信息
  ###
  @save: (promptData, isUpdate = false) ->
    # 数据验证（同步操作，不需要try-catch）
    unless promptData and ('object' is typeof promptData)
      return {error: 'Missing or invalid prompt data'}

    validation = @_validatePromptData(promptData, isUpdate)
    unless validation.valid
      return {error: validation.errors.join('; ')}

    # 准备保存数据
    now = new Date()
    saveData = Object.assign {}, promptData

    # 不允许手动设置时间戳字段
    delete saveData.ts
    delete saveData._mt

    # 设置时间戳
    saveData._mt = now

    try
      if isUpdate
        # 更新操作
        result = await PromptsCol.updateOne {_id: promptData._id}, {
          $set: saveData
          $setOnInsert: {ts: now}
        }, {upsert: true}

        if result.upsertedCount > 0
          return {
            success: true, _id: promptData._id, created: true, upsertedId: result.upsertedId
          }
        else
          return {
            success: true, _id: promptData._id, updated: true, modifiedCount: result.modifiedCount
          }
      else
        # 创建操作 - 先检查是否存在
        existing = await PromptsCol.findOne {_id: promptData._id}
        if existing
          return {error: 'Prompt template with this ID already exists'}

        # 设置创建时间
        saveData.ts = now

        # 插入新记录
        result = await PromptsCol.insertOne saveData
        return {success: true, _id: promptData._id, created: true, insertedId: result.insertedId}

    catch err
      debug.error 'Prompts.save error:', err
      return {error: MSG_STRINGS.DB_ERROR}

  ###
  创建新的prompt模板（向后兼容包装器）
  @param {Object} promptData - 模板数据
  @return {Object} 创建结果或错误信息
  ###
  @create: (promptData) ->
    return @save(promptData, false)

  ###
  根据ID获取单个prompt模板
  @param {String} id - 模板ID
  @return {Object} 模板数据或null
  ###
  @findById: (id) ->
    # 参数验证（同步操作）
    unless id and isStringWithValue(id)
      return null

    return await PromptsCol.findOne {_id: id}

  ###
  查询prompt模板列表
  @param {Object} options - 查询选项
  @param {String} options.status - 状态过滤
  @param {String} options.scenario - 场景过滤
  @param {Array} options.tags - 标签过滤
  @param {Number} options.limit - 限制数量
  @param {Number} options.skip - 跳过数量
  @param {Object} options.sort - 排序选项
  @return {Array} 模板列表
  ###
  @find: (options = {}) ->
    # 构建查询条件（同步操作）
    query = {}

    if options.status and options.status in VALID_STATUSES
      query.status = options.status

    if options.scenario and options.scenario in VALID_SCENARIOS
      query.scenario = options.scenario

    if options.tags and Array.isArray(options.tags) and options.tags.length > 0
      query.tags = {$in: options.tags}

    # 构建查询选项
    queryOptions = {}

    if options.limit and isNumber(options.limit) and options.limit > 0
      queryOptions.limit = options.limit

    if options.skip and isNumber(options.skip) and options.skip >= 0
      queryOptions.skip = options.skip

    # 默认按版本和更新时间排序
    queryOptions.sort = options.sort or {ver: -1, _mt: -1}

    return await PromptsCol.findToArray query, queryOptions

  ###
  更新prompt模板（向后兼容包装器）
  @param {String} id - 模板ID
  @param {Object} updateData - 更新数据
  @return {Object} 更新结果
  ###
  @update: (id, updateData) ->
    # 参数验证（同步操作）
    unless id and isStringWithValue(id)
      return {error: 'Missing or invalid id'}

    unless updateData and typeof updateData is 'object'
      return {error: 'Missing or invalid update data'}

    # 准备完整的数据对象用于保存
    # 首先获取现有数据，然后合并更新数据
    try
      existing = await PromptsCol.findOne {_id: id}
    catch err
      debug.error 'Prompts.update findOne error:', err
      return {error: MSG_STRINGS.DB_ERROR}

    unless existing
      return {error: 'Prompt template not found'}

    # 合并现有数据和更新数据
    mergedData = Object.assign {}, existing, updateData
    mergedData._id = id  # 确保ID正确

    # 使用save函数进行更新
    return @save(mergedData, true)

  ###
  删除prompt模板
  @param {String} id - 模板ID
  @return {Object} 删除结果
  ###
  @delete: (id) ->
    # 参数验证（同步操作）
    unless id and isStringWithValue(id)
      return {error: 'Missing or invalid id'}

    try
      result = await PromptsCol.deleteOne {_id: id}

      if result.deletedCount is 0
        return {error: 'Prompt template not found'}

      return {success: true, deletedCount: result.deletedCount}
    catch err
      debug.error 'Prompts.delete error:', err
      return {error: MSG_STRINGS.DB_ERROR}

  ###
  根据场景获取活跃的prompt模板
  @param {String} scenario - 应用场景
  @param {Object} options - 查询选项
  @return {Array} 活跃模板列表
  ###
  @findActiveByScenario: (scenario, options = {}) ->
    # 参数验证（同步操作）
    unless scenario and scenario in VALID_SCENARIOS
      return []

    # 构建查询条件
    query = {
      scenario: scenario
      status: 'active'
    }

    # 支持标签过滤
    if options.tags and Array.isArray(options.tags) and options.tags.length > 0
      query.tags = {$in: options.tags}

    queryOptions = {
      sort: {ver: -1, _mt: -1}
      limit: options.limit or 50
    }

    return await PromptsCol.findToArray query, queryOptions

  ###
  获取最新版本的模板
  @param {String} baseId - 基础ID（不含版本号）
  @return {Object} 最新版本模板或null
  ###
  @findLatestVersion: (baseId) ->
    # 参数验证（同步操作）
    unless baseId and isStringWithValue(baseId)
      return null

    # 构建查询条件
    query = {
      _id: new RegExp("^#{baseId}_v\\d+$")
      status: {$ne: 'deprecated'}
    }

    queryOptions = {
      sort: {ver: -1}
      limit: 1
    }

    templates = await PromptsCol.findToArray query, queryOptions
    return if templates.length > 0 then templates[0] else null

  ###
  替换模板中的变量
  @param {Object} template - 模板对象
  @param {Object} variables - 变量值映射
  @return {Object} 处理结果，包含替换后的内容或错误信息
  ###
  @replaceVariables: (template, variables = {}) ->
    # 参数验证（同步操作）
    unless template?.tpl?.main
      return {error: 'Invalid template structure'}

    # 检查必填变量
    if template.vars
      for variable in template.vars
        if variable.req and not variables[variable.nm]?
          return {error: "Missing required variable: #{variable.nm}"}

    # 变量替换逻辑（同步操作，不需要try-catch）
    # 替换主模板内容中的变量
    mainContent = template.tpl.main
    for varName, varValue of variables
      # 使用正则表达式替换 {变量名} 格式的占位符
      regex = new RegExp("\\{#{varName}\\}", 'g')
      mainContent = mainContent.replace(regex, String(varValue))

    # 替换系统提示词中的变量（如果存在）
    sysContent = template.tpl.sys
    if sysContent
      for varName, varValue of variables
        regex = new RegExp("\\{#{varName}\\}", 'g')
        sysContent = sysContent.replace(regex, String(varValue))

    result = {
      main: mainContent
      sys: sysContent
      model: template.m_cfg?.m_nm
      params: template.m_cfg?.params or {}
    }

    return {success: true, content: result}

  ###
  统计各状态的模板数量
  @return {Object} 统计结果
  ###
  @getStatistics: () ->
    # 构建聚合管道（同步操作）
    statusPipeline = [
      {
        $group: {
          _id: '$status'
          count: {$sum: 1}
        }
      }
    ]

    scenarioPipeline = [
      {
        $group: {
          _id: '$scenario'
          count: {$sum: 1}
        }
      }
    ]

    try
      # 并行执行多个数据库查询
      [statusStats, scenarioStats, totalCount] = await Promise.all([
        PromptsCol.aggregate(statusPipeline).toArray()
        PromptsCol.aggregate(scenarioPipeline).toArray()
        PromptsCol.countDocuments {}
      ])

      return {
        total: totalCount
        byStatus: statusStats
        byScenario: scenarioStats
      }
    catch err
      debug.error 'Prompts.getStatistics error:', err
      throw MSG_STRINGS.DB_ERROR

  ###
  批量更新模板状态
  @param {Array} ids - 模板ID数组
  @param {String} status - 新状态
  @return {Object} 更新结果
  ###
  @batchUpdateStatus: (ids, status) ->
    # 参数验证（同步操作）
    unless Array.isArray(ids) and ids.length > 0
      return {error: 'Missing or invalid ids array'}

    unless status in VALID_STATUSES
      return {error: "Invalid status. Must be one of: #{VALID_STATUSES.join(', ')}"}

    try
      result = await PromptsCol.updateMany {
        _id: {$in: ids}
      }, {
        $set: {
          status: status
          _mt: new Date()
        }
      }

      return {
        success: true
        matchedCount: result.matchedCount
        modifiedCount: result.modifiedCount
      }
    catch err
      debug.error 'Prompts.batchUpdateStatus error:', err
      return {error: MSG_STRINGS.DB_ERROR}

  ###
  验证模板数据结构（公共接口，使用内部验证函数）
  @param {Object} templateData - 模板数据
  @return {Object} 验证结果
  ###
  @validateTemplate: (templateData) ->
    return @_validatePromptData(templateData, false)

MODEL 'Prompts', Prompts
